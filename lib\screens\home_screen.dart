import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'zone_screen.dart';
import '../widgets/sidebar.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<dynamic> zones = [];
  bool isLoading = true;

  final List<Color> zoneColors = [
    Colors.blue, // East Zone
    Colors.green, // West Zone
    Colors.orange, // Center Zone
  ];

  final List<IconData> zoneIcons = [
    Icons.east, // East Zone
    Icons.west, // West Zone
    Icons.gps_fixed, // Center Zone
  ];

  @override
  void initState() {
    super.initState();
    fetchZones();
  }

  Future<void> fetchZones() async {
    final String apiUrl = 'http://app.rmcwwscada.com:8081/api/Values/zoneNameList';
    final String username = 'ClearWaterWebAPI';
    final String password = 'RMC<PERSON>le@r308';
    final String basicAuth =
        'Basic ' + base64Encode(utf8.encode('$username:$password'));

    try {
      if (!mounted) return;
      setState(() {
        isLoading = true;
      });

      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {'Authorization': basicAuth},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (!mounted) return;
        setState(() {
          // Filter only Zone IDs 3, 4, and 5
          zones =
              data['zonelist'].where((zone) {
                return zone['intId'] == 3 ||
                    zone['intId'] == 4 ||
                    zone['intId'] == 5;
              }).toList();
          isLoading = false;
        });
      } else {
        throw Exception('Failed to load zones');
      }
    } catch (error) {
      if (!mounted) return; // ✅ Prevents setState after dispose
      setState(() => isLoading = false);
      print('Error fetching zones: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisSize: MainAxisSize.min, // Prevents shifting
          children: [
            Flexible(
              child: Image.asset(
                'assets/rmc_logo.png',
                height: 40, // Adjust logo height
              ),
            ),
            SizedBox(width: 10),
            Text(
              'RMC Water Works',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        centerTitle: true, // Keeps content centered
        /*   actions: [
          isLoading
              ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.transparent,
                    strokeWidth: 2,
                  ),
                ),
              )
              : IconButton(
                icon: Icon(Icons.refresh),
                onPressed: fetchZones,
                tooltip: "Refresh",
              ),
        ],*/
      ),
      drawer: Sidebar(),
      body:
          isLoading
              ? Center(child: CircularProgressIndicator())
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: ListView.builder(
                  itemCount: zones.length,
                  itemBuilder: (context, index) {
                    var zone = zones[index];

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 10.0),
                      child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => ZoneScreen(
                                    zoneId: zone['intId'],
                                    zoneName: zone['strZoneName'],
                                  ),
                            ),
                          );
                        },
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(vertical: 20),
                          decoration: BoxDecoration(
                            color: zoneColors[index % zoneColors.length],
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black26,
                                blurRadius: 5,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                zoneIcons[index % zoneIcons.length],
                                size: 50,
                                color: Colors.white,
                              ),
                              SizedBox(height: 10),
                              Text(
                                zone['strZoneName'],
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
    );
  }
}
