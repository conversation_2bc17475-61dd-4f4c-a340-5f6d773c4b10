import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'location_screen.dart';

class ZoneScreen extends StatefulWidget {
  final int zoneId;
  final String zoneName;
  ZoneScreen({required this.zoneId, required this.zoneName});

  @override
  _ZoneScreenState createState() => _ZoneScreenState();
}

class _ZoneScreenState extends State<ZoneScreen> {
  List<dynamic> locations = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchLocations();
  }

  Future<void> fetchLocations() async {
    final String apiUrl = 'http://app.rmcwwscada.com:8081/api/Values/plantInfo';
    final String username = 'ClearWaterWebAPI';
    final String password = 'RMCCle@r308';
    final String basicAuth =
        'Basic ' + base64Encode(utf8.encode('$username:$password'));

    try {
        if (!mounted) return;
      setState(() {
        isLoading = true;
      });
    
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Authorization': basicAuth,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {'intZoneId': widget.zoneId.toString(), 'intDomainId': '1'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final filteredLocations =
            (data['plantList'] as List)
                .where(
                  (plant) =>
                      plant['dtCreatedDate'] !=
                          null && // Ensure the field is not null
                      !(plant['dtCreatedDate'] as String).contains('0001'),
                ) // Exclude invalid dates
                .toList();
        if (!mounted) return; // ✅ Prevents setState if widget is disposed

        setState(() {
          locations = filteredLocations;
          isLoading = false;
        });
      } else {
        throw Exception('Failed to load locations');
      }
    } catch (error) {
       if (!mounted) return; // ✅ Prevents setState after dispose
      setState(() => isLoading = false);
      print('Error fetching locations: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisSize: MainAxisSize.min, // Prevents shifting
          children: [
            Flexible(
              child: Image.asset(
                'assets/rmc_logo.png',
                height: 40, // Adjust logo height
              ),
            ),
            SizedBox(width: 10),
            Text(
              widget.zoneName,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        centerTitle: true, // Keeps content centered
         actions: [
          isLoading
              ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.transparent,
                    strokeWidth: 2,
                  ),
                ),
              )
              : IconButton(
                icon: Icon(Icons.refresh),
                onPressed: fetchLocations,
                tooltip: "Refresh",
              ),
        ],
      ),
      body:
          isLoading
              ? Center(child: CircularProgressIndicator())
              : Padding(
                padding: EdgeInsets.all(12.0),
                child: ListView.builder(
                  itemCount: locations.length,
                  itemBuilder: (context, index) {
                    var location = locations[index];
                    return Container(
                      margin: EdgeInsets.symmetric(vertical: 8.0),
                      padding: EdgeInsets.all(12.0),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue, width: 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.2),
                            spreadRadius: 2,
                            blurRadius: 5,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: ListTile(
                        contentPadding:  EdgeInsets.zero, 
                        dense: false,
                        visualDensity: VisualDensity(vertical: -3),
                        title: Text(
                          location['strPlantName'],
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade900,
                          ),
                        ),
                        subtitle: Text(
                          'Last update: ${location['dtCreatedDate']}',
                          style: TextStyle(color: Colors.black54),
                        ),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => LocationScreen(
                                    plantId: location['intPlantId'],
                                    plantName: location['strPlantName'],
                                  ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
    );
  }
}
