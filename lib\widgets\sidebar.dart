import 'package:flutter/material.dart';
import '../screens/home_screen.dart';

class Sidebar extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(color: Colors.blue),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/rmc_logo.png',
                  height: 70, // Increased logo size but keeping it balanced
                ),
                SizedBox(height: 10),
                Text(
                  'Rajkot Municipal Corporation', // Kept as a single line
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18, // Keeping it readable
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  softWrap: true, // Ensures no overflow
                ),
              ],
            ),
          ),
          ListTile(
            leading: Icon(Icons.home),
            title: Text('Home'),
            onTap: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => HomeScreen()),
              );
            },
          ),
        ],
      ),
    );
  }
}
