import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class LocationScreen extends StatefulWidget {
  final int plantId;
  final String plantName;
  LocationScreen({required this.plantId, required this.plantName});

  @override
  _LocationScreenState createState() => _LocationScreenState();
}

class _LocationScreenState extends State<LocationScreen> {
  Map<String, dynamic> plantDetails = {};
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchPlantDetails();
  }

  Future<void> fetchPlantDetails() async {
    final String apiUrl =
        'http://app.rmcwwscada.com:8081/api/Values/plantDetail';
    final String username = 'ClearWaterWebAPI';
    final String password = 'RMCCle@r308';
    final String basicAuth =
        'Basic ' + base64Encode(utf8.encode('$username:$password'));

    try {
      if (!mounted) return;
      setState(() {
        isLoading = true;
      });
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Authorization': basicAuth,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {'intPlantId': widget.plantId.toString()},
      );

      if (response.statusCode == 200) {
        if (!mounted) return;
        setState(() {
          plantDetails = json.decode(response.body);
          isLoading = false;
        });
      } else {
        throw Exception('Failed to load plant details');
      }
    } catch (error) {
      if (!mounted) return; // ✅ Prevents setState after dispose
      setState(() => isLoading = false);
      print('Error fetching plant details: $error');
    }
  }

  /// **Function to Format Key Names**
  String formatKey(String key) {
    if (key == "dtDatetime") return "Date & Time"; // Rename dtDatetime
    return key
        .replaceAll("_", " ") // Replace underscores with spaces
        .replaceAll("M3 H", "m³/h") // Replace "M3 H" with "m³/Hr"
        .trim();
  }

  /// **Function to Format Values**
  String formatValue(String key, dynamic value) {
    if (value.toString() == "12345678.90") return "Arriving..";
    if (value.toString() == "12345678.9") return "Arriving..";
    if (key == "dtDatetime") return value.toString().replaceAll("T", " ");
    // Remove "T" from dtDatetime
    return value.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.min, // Prevents shifting
          children: [
            Image.asset(
              'assets/rmc_logo.png',
              height: 40, // Adjust logo height
            ),
            SizedBox(width: 10),
            Expanded(
              child: Text(
                widget.plantName,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                overflow:
                    TextOverflow.ellipsis, // ✅ Truncates long text with "..."
                maxLines: 1, // ✅ Limits text to one line
                softWrap: false, // ✅ Prevents breaking into multiple lines
              ),
            ),
          ],
        ),
        centerTitle: true, // Keeps content centered
        actions: [
          isLoading
              ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.transparent,
                    strokeWidth: 2,
                  ),
                ),
              )
              : IconButton(
                icon: Icon(Icons.refresh),
                onPressed: fetchPlantDetails,
                tooltip: "Refresh",
              ),
        ],
      ), // Show plant name from Zone Screen
      body:
          isLoading
              ? Center(child: CircularProgressIndicator())
              : Padding(
                padding: EdgeInsets.all(16.0),
                child: Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        /// **Show Plant Name received from Zone Screen**
                        Text(
                          widget.plantName, // Uses plant name from Zone Screen
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                        SizedBox(height: 10),
                        Divider(color: Colors.blue.shade300),

                        /// **Scrollable Table for Parameters**
                        Expanded(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.vertical,
                            child: Table(
                              border: TableBorder.all(
                                color: Colors.blue.shade100,
                                width: 1,
                              ),
                              columnWidths: {
                                0: FlexColumnWidth(2), // Key column
                                1: FlexColumnWidth(3), // Value column
                              },
                              children:
                                  plantDetails.entries
                                      .where(
                                        (entry) =>
                                            entry.key != "dtDay" &&
                                            entry.key != "dtMonth" &&
                                            entry.key != "dtYear",
                                      ) // Remove unwanted keys
                                      .map((entry) {
                                        return TableRow(
                                          decoration: BoxDecoration(
                                            color:
                                                plantDetails.entries
                                                                .toList()
                                                                .indexOf(
                                                                  entry,
                                                                ) %
                                                            2 ==
                                                        0
                                                    ? Colors.blue.shade50
                                                    : Colors.white,
                                          ),
                                          children: [
                                            Padding(
                                              padding: EdgeInsets.all(10),
                                              child: Text(
                                                formatKey(
                                                  entry.key,
                                                ), // Format key names
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                            Padding(
                                              padding: EdgeInsets.all(10),
                                              child: Text(
                                                formatValue(
                                                  entry.key,
                                                  entry.value,
                                                ), // Format values
                                                style: TextStyle(
                                                  color: Colors.black87,
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      })
                                      .toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
    );
  }
}
